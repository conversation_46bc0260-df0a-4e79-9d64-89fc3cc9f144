#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import inspect

from logear.autolog_exception import default_shall_wrap, adapt_autolog_exception


class TestAsyncMethodsExclusion:
    """Test class to verify that async methods are excluded from auto-exception logging."""

    def test_default_shall_wrap_excludes_async_methods(self):
        """Test that default_shall_wrap returns False for async methods."""

        class TestClass:
            def regular_method(self):
                return "regular method"

            async def async_method(self):
                return "async method"

            def _private_method(self):
                return "private method"

        # Regular method should be wrapped
        assert default_shall_wrap(TestClass, "regular_method") is True

        # Async method should not be wrapped
        assert default_shall_wrap(TestClass, "async_method") is False

        # Private method should not be wrapped (existing behavior)
        assert default_shall_wrap(TestClass, "_private_method") is False

    def test_adapt_autolog_exception_with_async_methods(self):
        """Test that adapt_autolog_exception correctly skips async methods."""

        @adapt_autolog_exception()
        class TestClassWithAsync:
            def regular_method(self):
                return "regular method"

            async def async_method(self):
                return "async method"

        # Check that the regular method was wrapped
        instance = TestClassWithAsync()
        regular_method = getattr(instance, "regular_method")
        assert hasattr(regular_method, "__wrapped__") or inspect.ismethod(regular_method)

        # Check that the async method was not wrapped
        async_method = getattr(instance, "async_method")
        assert inspect.iscoroutinefunction(async_method)
        # The async method should not have been wrapped, so it should still be a coroutine function
        # If it was wrapped, it would no longer be identified as a coroutine function
