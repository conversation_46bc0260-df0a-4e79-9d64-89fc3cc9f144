#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import functools
from icecream import ic


class UniversalDecoratorSample:
    def __init__(self, wrapped_item):
        self.wrapped_item = ic(wrapped_item)
        self._update_wrapper_for_init()

    def _update_wrapper_for_init(self):
        try:
            functools.update_wrapper(self, self.wrapped_item)
        except AttributeError:
            target_for_update = self.wrapped_item
            if isinstance(target_for_update, (classmethod, staticmethod)):
                target_for_update = target_for_update.__func__
            if isinstance(target_for_update, property) and target_for_update.fget:
                target_for_update = target_for_update.fget

            functools.update_wrapper(self, target_for_update)

    def _is_fundamentally_property(self) -> bool:
        item = self.wrapped_item
        if isinstance(item, property): return True
        if isinstance(item, (classmethod, staticmethod)) and isinstance(item.__func__, property): return True
        return False

    def _get_core_property_and_outer_wrapper(self):
        # Returns: (core_property_or_None, outer_wrapper_type_or_None, original_callable_if_no_property_or_None)
        if isinstance(self.wrapped_item, property):
            return self.wrapped_item, None, None
        elif isinstance(self.wrapped_item, (classmethod, staticmethod)):
            inner_func = self.wrapped_item.__func__
            if isinstance(inner_func, property):
                return inner_func, type(self.wrapped_item), None
            else: # e.g., @MUD @classmethod def foo(): ...
                return None, type(self.wrapped_item), inner_func
        elif callable(self.wrapped_item): # A simple function
            return None, None, self.wrapped_item
        else:
            # This case should ideally not be hit if used as a decorator on common class elements
            raise TypeError(f"Wrapped item is {type(self.wrapped_item).__name__}, not a recognized type for property operations.")

    def __get__(self, instance, owner):
        # Initial check for non-descriptors or items that don't have __get__
        if not hasattr(self.wrapped_item, '__get__'):
            if instance is not None and callable(self.wrapped_item):
                # Avoid re-binding if already bound (e.g. functools.partial or method already bound)
                is_already_bound = ic(
                    isinstance(self.wrapped_item, functools.partial) or \
                    (hasattr(self.wrapped_item, '__self__') and self.wrapped_item.__self__ is not None))
                if not is_already_bound:
                    return ic(functools.partial(self.wrapped_item, instance))
            return ic(self.wrapped_item)

        if self._is_fundamentally_property():
            # Special handling for @MUD @staticmethod @property
            if isinstance(self.wrapped_item, staticmethod) and \
               isinstance(self.wrapped_item.__func__, property):
                # self.wrapped_item is staticmethod(property_obj)
                # self.wrapped_item.__func__ is property_obj
                actual_property_object = self.wrapped_item.__func__
                if actual_property_object.fget:
                    # The fget of this property is the original static function.
                    # Call it directly to get the value.
                    # print(f"MUD StaticProp: Calling fget: {actual_property_object.fget}")
                    return ic(actual_property_object.fget())
                else:
                    # No getter, standard behavior is to raise AttributeError or return property
                    # For consistency, if MUD is to make it "just work", raising might be better if unreadable
                    raise AttributeError("unreadable static property")

            # For @MUD @property or @MUD @classmethod @property:
            # self.wrapped_item.__get__ already returns the final value.
            # print(f"MUD Prop/ClassProp: Delegating to {self.wrapped_item}.__get__")
            return ic(self.wrapped_item.__get__(instance, owner))
        else:
            # It's a method (regular, class, or static but not wrapping a property)
            # resolved_item is the 'next_executable' (bound method or raw function)
            resolved_item = ic(self.wrapped_item.__get__(instance, owner))
            next_executable = resolved_item
            @functools.wraps(next_executable)
            def actual_execution_wrapper(*args, **kwargs):
                # --- Your pre-call logic here ---
                # print(f"MUD Wrapper: Calling {next_executable} with {args}, {kwargs}")
                result = ic(next_executable(*args, **kwargs))
                # --- Your post-call logic here ---
                # print(f"MUD Wrapper: Result {result}")
                return result
            return actual_execution_wrapper

    def __set__(self, instance, value):
        core_property, _, _ = self._get_core_property_and_outer_wrapper()
        if core_property and hasattr(core_property, '__set__'):
            core_property.__set__(instance, value)
            return
        # Fallback for other data descriptors MUD might wrap directly (less common for this decorator's intent)
        elif hasattr(self.wrapped_item, '__set__') and isinstance(self.wrapped_item, property):
             self.wrapped_item.__set__(instance, value)
             return
        name = "attribute";
        try:
            if core_property and core_property.fget: name = core_property.fget.__name__
        except: pass
        raise AttributeError(f"can't set attribute '{name}', not a settable property or descriptor")

    def __delete__(self, instance):
        core_property, _, _ = self._get_core_property_and_outer_wrapper()
        if core_property and hasattr(core_property, '__delete__'):
            core_property.__delete__(instance)
            return
        elif hasattr(self.wrapped_item, '__delete__') and isinstance(self.wrapped_item, property):
            self.wrapped_item.__delete__(instance)
            return
        name = "attribute";
        try:
            if core_property and core_property.fget: name = core_property.fget.__name__
        except: pass
        raise AttributeError(f"can't delete attribute '{name}', not a deletable property or descriptor")

    def getter(self, fget):
        core_property, outer_wrapper_type, original_callable = self._get_core_property_and_outer_wrapper()
        if core_property: new_core_property = core_property.getter(fget)
        elif original_callable: new_core_property = property(fget=fget) # Make it a property
        else: raise TypeError(f"Cannot define getter for wrapped item of type {type(self.wrapped_item).__name__}")
        intermediate_wrapped_item = outer_wrapper_type(new_core_property) if outer_wrapper_type else new_core_property
        return type(self)(intermediate_wrapped_item)

    def setter(self, fset):
        core_property, outer_wrapper_type, original_callable = self._get_core_property_and_outer_wrapper()
        if core_property: new_core_property = core_property.setter(fset)
        elif original_callable: new_core_property = property(fget=original_callable).setter(fset) # Use original as fget
        else: raise TypeError(f"Cannot define setter for wrapped item of type {type(self.wrapped_item).__name__}")
        intermediate_wrapped_item = outer_wrapper_type(new_core_property) if outer_wrapper_type else new_core_property
        return type(self)(intermediate_wrapped_item)

    def deleter(self, fdel):
        core_property, outer_wrapper_type, original_callable = self._get_core_property_and_outer_wrapper()
        if core_property: new_core_property = core_property.deleter(fdel)
        elif original_callable: new_core_property = property(fget=original_callable).deleter(fdel) # Use original as fget
        else: raise TypeError(f"Cannot define deleter for wrapped item of type {type(self.wrapped_item).__name__}")
        intermediate_wrapped_item = outer_wrapper_type(new_core_property) if outer_wrapper_type else new_core_property
        return type(self)(intermediate_wrapped_item)
