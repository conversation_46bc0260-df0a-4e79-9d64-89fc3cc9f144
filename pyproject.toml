[tool.poetry]
name = "logear"
version = "1.2.3"
description = "Ease cumbersome of logger configuration."
authors = ["Art <<EMAIL>>"]
license = "Apache-2.0"
readme = "README.md"
packages = [{include = "logear", from = "src"}]

[tool.poetry.dependencies]
python = "^3.10"
decursion = {git = "**************:artingitlab/decursion.git"}
icecream = "^2.1.3"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.5"
ipython = "^8.14.0"
pytest-ordering = "^0.6"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
testpaths = "tests"
