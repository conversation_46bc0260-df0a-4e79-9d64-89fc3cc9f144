**Product Requirements Document: AutoLog Exception Utility**

**1. Introduction**
The AutoLog Exception Utility is a Python library designed to simplify and automate the process of logging unhandled exceptions within functions, methods, and properties. It achieves this through Python decorators that can be applied at the individual callable level or to an entire class. The utility aims to provide developers with immediate, contextualized information about runtime errors, facilitating faster debugging and improved code reliability. 

**2. Goals**
*   **Automate Exception Logging:** Reduce boilerplate code required for try-except-log-raise patterns.
*   **Contextual Logging:** Provide logger names and log messages that help pinpoint the source of an exception (module, class, instance, function).
*   **Flexibility:** Allow developers to use their own `logging.Logger` instances or rely on automatically configured loggers.
*   **Ease of Use:** Simple application via decorators.
*   **Comprehensive Coverage:** Support for regular functions, instance methods, class methods, static methods, and properties.
*   **Safety:** Prevent common issues like infinite recursion caused by logging within the logging mechanism itself.

**3. Target Audience**
Python developers who want to:
*   Improve the robustness of their applications by ensuring exceptions are logged.
*   Reduce the manual effort involved in setting up exception logging.
*   Standardize exception logging practices across their projects.

**4. Core Features (Functional Requirements)**

*   **FR1: Decorator for Individual Callables (`@auto_exception_logging`)**
    *   **FR1.1:** Shall provide a decorator factory `auto_exception_logging` that can be applied to functions, methods (instance, class, static), and properties.
    *   **FR1.2:** When a decorated callable raises an unhandled exception, the utility must log the exception details.
    *   **FR1.3:** After logging, the original exception must be re-raised.
    *   **FR1.4:** The decorator factory shall accept an optional `logger: logging.Logger` argument.
        *   **FR1.4.1:** If a `logger` is provided, it must be used for logging exceptions from the decorated callable.
        *   **FR1.4.2:** If no `logger` is provided, a logger instance must be automatically determined or created (see FR5).
    *   **FR1.5:** The decorator must preserve the metadata of the original callable (e.g., `__name__`, `__doc__`, signature) using `functools.wraps`.
    *   **FR1.6:** The decorator must detect and prevent infinite recursion if the decorated function calls itself in a way that triggers repeated exception logging (e.g., using a library like `decursion`).
    *   **FR1.7:** The decorator should raise a `ValueError` if applied directly to a class type.

*   **FR2: Decorator for Classes (`@adapt_autolog_exception`)**
    *   **FR2.1:** Shall provide a class decorator factory `adapt_autolog_exception`.
    *   **FR2.2:** This decorator factory shall accept an optional `logger: logging.Logger` argument. If provided, this logger will be passed to `auto_exception_logging` for all members it wraps.
    *   **FR2.3:** This decorator factory shall accept an optional `shall_wrap: Callable[[Type, str], bool]` predicate.
        *   **FR2.3.1:** `shall_wrap` determines if a specific member (identified by class and member name) of the decorated class should be wrapped with `auto_exception_logging`.
        *   **FR2.3.2:** A default `shall_wrap` implementation must be provided. This default shall:
            *   Exclude members whose names start with an underscore (`_`).
            *   Exclude asynchronous methods/coroutines. This will be one of main future considerations in future development.
    *   **FR2.4:** The decorator shall iterate over the members of the class. For each member that is a callable (function, method, static method, class method) or a property and satisfies the `shall_wrap` predicate, it shall apply the `auto_exception_logging` decorator.
    *   **FR2.5:** The decorator shall add a utility method named `grab_logger` to the decorated class if one doesn't already exist.
        *   **FR2.5.1:** `grab_logger(*args)` (typically `grab_logger(self)` when called on an instance):
            *   If a `logger` was provided to `adapt_autolog_exception` (FR2.2), this method shall return that logger.
            *   If called on an instance of the class (`args[0]` is an instance): It should return a logger instance specific to that object instance.
            *   Otherwise (e.g., called without instance context, or if instance-specific logger derivation fails): It should return a logger based on a "sample" callable member from the class.
        *   **FR2.5.2:** This method should raise a `RuntimeError` if no suitable logger can be determined (e.g., if a class has no wrappable members to derive a logger from).

*   **FR3: Logger Name Generation**
    *   **FR3.1:** The utility must generate fully qualified names for callables (e.g., `module.qualname`).
        *   **FR3.1.1:** For properties, the fully qualified name of their `fget` (getter) method, `fset` (setter) method, or `fdel` (deleter) method should be used.
        *   **FR3.1.2:** Handle cases where `__qualname__` might not be present but `__class__` is (e.g., callable instances).
    *   **FR3.2:** Logger names should be generated based on the context of the callable:
        *   **FR3.2.1:** For non-bound callables (functions, static methods not associated with a specific instance for logging context): The logger name should be derived from the module part of the callable's fully qualified name (e.g., `module_name`).
        *   **FR3.2.2:** For instance-bound callables (instance methods, properties accessed on an instance): The logger name should incorporate the instance's module, class name, and a unique identifier for the instance (e.g., `hex(hash(self_obj))`), formatted like: `instance_module.ClassName_instanceHash`.
        *   **FR3.2.3:** For class-bound callables (class methods): The logger name should incorporate the class's module, class name, and a unique identifier for the class (e.g., `hex(hash(cls_obj))`), formatted like: `class_module.ClassName_classHash`. (Note: current code treats `__self__` for class methods, which is the class itself).

*   **FR4: Automatic Logger Handler Configuration (`add_log_handlers`)**
    *   **FR4.1:** If a logger instance is automatically created/retrieved by the utility (i.e., not provided by the user), it must be configured with default handlers if it doesn't already have them.
    *   **FR4.2:** Check for existing `FileHandler` and `StreamHandler` types.
    *   **FR4.3:** If no `FileHandler` exists, add one:
        *   The log file shall be created in a temporary location (e.g., using `tempfile.mkstemp`).
        *   The log file name shall be prefixed with the logger's name and suffixed with a standard log extension (e.g., `.log`).
    *   **FR4.4:** If no `StreamHandler` (console output) exists, add one.
    *   **FR4.5:** Both automatically added handlers must use a standard log format: `%(asctime)s - %(name)s - %(levelname)s - %(message)s`.
    *   **FR4.6:** The default logging level for automatically configured loggers/handlers should be appropriate for capturing exceptions (e.g., `logging.ERROR` or `logging.INFO` if the logger is also used for other purposes by the wrapper). The current code implicitly relies on the default logger level (WARNING) and `logger.exception` which logs at ERROR level.

*   **FR5: Logger Retrieval (`get_logger_by_callable`, `get_logger_for_property`)**
    *   **FR5.1:** Provide a function `get_logger_by_callable(callee)` that:
        *   Generates a logger name based on `callee` (see FR3.2).
        *   Gets/creates a `logging.Logger` instance for that name.
        *   Configures it with default handlers using `add_log_handlers` (FR4).
        *   Returns the logger instance.
    *   **FR5.2:** Provide a function `get_logger_for_property(prop, owner_instance)` that:
        *   Generates a logger name based on the `owner_instance` (similar to FR3.2.2). It must validate that `prop` is indeed a property of `owner_instance`.
        *   Gets/creates a `logging.Logger` instance for that name.
        *   Configures it with default handlers using `add_log_handlers` (FR4).
        *   Returns the logger instance.

*   **FR6: Log Content**
    *   **FR6.1:** When an exception is logged, it must use `logger.exception()` to include exception information and stack trace (`stack_info=True`).

**5. Non-Functional Requirements**
*   **NFR1: Performance:** The overhead introduced by the decorators should be minimal for calls that do not raise exceptions.
*   **NFR2: Reliability:** The utility must correctly handle various types of callables and edge cases (e.g., decorated decorators, complex inheritance) without introducing new errors.
*   **NFR3: Usability:** Decorators should be intuitive and require minimal configuration for common use cases.
*   **NFR4: Python Compatibility:** Specify target Python versions (e.g., Python 3.7+).
*   **NFR5: Thread Safety:** Considerations for thread safety if loggers or shared state are accessed concurrently (standard Python logging is thread-safe; custom logic needs care).
*   **NFR6: Dependency Management:** Minimize external dependencies. `decursion` is one such dependency.

**6. Future Considerations / Out of Scope for This Iteration (but noted from current code/comments)**
*   **FC1:** Allow users to specify a custom directory for automatically created log files (currently uses `tempfile`).
*   **FC2:** More sophisticated or direct support for `async` callables.
*   **FC3:** User-configurable log formats and levels for auto-configured handlers.
*   **FC4:** Options to control `stack_info` or other `logger.exception` parameters.

**7. Definitions/Glossary**
*   **Callable:** Any object that can be called using the `()` operator (functions, methods, etc.).
*   **Property:** An object managed by `property()`, typically used for managed attributes.
*   **FQN (Fully Qualified Name):** A name that uniquely identifies an object in Python, typically `module.qualname`.
*   **Decorator:** A design pattern in Python that allows adding functionality to an existing object (like a function or class) without modifying its structure.
*   **Handler (Logging):** A component in Python's `logging` module that determines what happens to a log record (e.g., write to file, print to console).
